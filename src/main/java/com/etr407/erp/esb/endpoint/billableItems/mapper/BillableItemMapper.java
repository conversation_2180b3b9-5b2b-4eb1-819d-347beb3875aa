package com.etr407.erp.esb.endpoint.billableItems.mapper;

import com.etr407.erp.esb.endpoint.billableItems.model.canonical.BillableItem;
import com.etr407.erp.esb.endpoint.billableItems.model.canonical.BillableItemsResponse;
import com.etr407.erp.esb.endpoint.billableItems.model.sap.SAPBillableItem;
import com.etr407.erp.esb.endpoint.billableItems.model.sap.SAPBillableItemsResponse;
import org.mapstruct.*;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;

/**
 * MapStruct mapper for converting SAP billable items to canonical billable items
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE, uses = {})
public interface BillableItemMapper {

    /**
     * Maps a SAP billable items response to a canonical billable items response
     *
     * @param sapResponse The SAP billable items response
     * @return Canonical billable items response
     */
    @Mapping(target = "items", source = "row")
    BillableItemsResponse mapToBillableItemsResponse(SAPBillableItemsResponse sapResponse);

    /**
     * After mapping, ensure items is never null
     */
    @AfterMapping
    default void ensureItemsNotNull(@MappingTarget BillableItemsResponse target) {
        if (target.getItems() == null) {
            target.setItems(new ArrayList<>());
        }
    }

    /**
     * Maps a list of SAP billable items to a list of canonical billable items
     *
     * @param sapItems The list of SAP billable items
     * @return List of canonical billable items
     */
    List<BillableItem> mapToBillableItems(List<SAPBillableItem> sapItems);

    /**
     * Maps a SAP billable item to a canonical billable item
     *
     * @param sapItem The SAP billable item
     * @return Canonical billable item
     */
    @Mappings({
            // Basic field mappings - using qualifiedByName to avoid ambiguity
            @Mapping(target = "billableItemAmt", source = "billableItemAmt", qualifiedByName = "defaultStringMapping"),
            @Mapping(target = "billableItemText", source = "billableItemText", qualifiedByName = "defaultStringMapping"),
            @Mapping(target = "tripId", source = "tripId", qualifiedByName = "defaultStringMapping"),
            @Mapping(target = "dateOfOrigin", source = "dateOfOrigin", qualifiedByName = "defaultStringMapping"),
            @Mapping(target = "timeOfOrigin", source = "timeOfOrigin", qualifiedByName = "defaultStringMapping"),
            @Mapping(target = "jurisdictionCode", source = "jurisdictionCode", qualifiedByName = "defaultStringMapping"),
            @Mapping(target = "billingQuantity", source = "billingQuantity", qualifiedByName = "defaultStringMapping"),
            @Mapping(target = "continuousTripId", source = "continuousTripId", qualifiedByName = "defaultStringMapping"),
            @Mapping(target = "entryInterchangeName", source = "entryInterchange", qualifiedByName = "defaultStringMapping"),
            @Mapping(target = "exitDate", source = "exitDate", qualifiedByName = "defaultStringMapping"),
            @Mapping(target = "exitInterchangeName", source = "exitInterchange", qualifiedByName = "defaultStringMapping"),
            @Mapping(target = "exitTime", source = "exitTime", qualifiedByName = "defaultStringMapping"),
            @Mapping(target = "rateDate", source = "rateDate", qualifiedByName = "defaultStringMapping"),
            @Mapping(target = "entryDate", source = "entryDate", qualifiedByName = "defaultStringMapping"),
            @Mapping(target = "entryTime", source = "entryTime", qualifiedByName = "defaultStringMapping"),
            @Mapping(target = "subscriptionType", source = "subscriptionType", qualifiedByName = "defaultStringMapping"),
            @Mapping(target = "subprocess", source = "subprocess", qualifiedByName = "defaultStringMapping"),
            @Mapping(target = "unitOfMeasure", source = "unitOfMeasure", qualifiedByName = "defaultStringMapping"),
            @Mapping(target = "capturedPlateId", source = "capturePlateId", qualifiedByName = "defaultStringMapping"),
            @Mapping(target = "unbillableTollingDeviceId", source = "unbillableDeviceId", qualifiedByName = "defaultStringMapping"),
            @Mapping(target = "unbillableReason", source = "unbillableReason", qualifiedByName = "defaultStringMapping"),
            @Mapping(target = "bitReversed", source = "bitReversed", qualifiedByName = "defaultStringMapping"),
            @Mapping(target = "billingCustomerId", source = "businessPartner", qualifiedByName = "defaultStringMapping"),
            @Mapping(target = "billingDocumentId", source = "billingDoc", qualifiedByName = "defaultStringMapping"),
            @Mapping(target = "sourceTransId", source = "srcTransId", qualifiedByName = "defaultStringMapping"),
            @Mapping(target = "tollingServiceId", source = "providerContractId", qualifiedByName = "defaultStringMapping"),

            // Custom mappings that need special handling
            @Mapping(target = "statusOfBillableItem", expression = "java(mapStatus(sapItem))"),
            @Mapping(target = "tripType", expression = "java(mapTripType(sapItem.getTripType()))"),
            @Mapping(target = "businessUnit", expression = "java(mapBusinessUnit(sapItem.getDivision()))"),
            @Mapping(target = "ratingMethod",
                    expression = "java(mapRatingMethod(sapItem.getRatingMethod(), mapTripType(sapItem.getTripType())))"),
            @Mapping(target = "billableItemType", expression = "java(mapBillableItemType(sapItem.getBillableItemType()))"),
            @Mapping(target = "ratedClass", expression = "java(mapRatedClass(sapItem.getRatedClass()))"),
            @Mapping(target = "tollingDeviceIndicator", expression = "java(mapTollingDeviceIndicator(sapItem.getTollingDeviceInd()))"),
            @Mapping(target = "tollingDeviceId", source = "tollingDeviceId", qualifiedByName = "defaultStringMapping"),
            @Mapping(target = "lowBatteryIndicator", source = "lowBatteryInd", qualifiedByName = "defaultStringMapping"),
            @Mapping(target = "continuousTripIndicator", source = "continuousTrip", qualifiedByName = "defaultStringMapping"),
            @Mapping(target = "suppressedAmt", source = "suppressedAmount", qualifiedByName = "defaultStringMapping"),
            @Mapping(target = "tripDuration", expression = "java(calcTripDuration(sapItem))")
    })
    BillableItem mapToBillableItem(SAPBillableItem sapItem);

    /**
     * Maps the SAP status to canonical status
     *
     * @param sapItem The SAP billable item
     * @return Canonical status
     */
    default String mapStatus(SAPBillableItem sapItem) {
        if (sapItem == null || sapItem.getStatus() == null) {
            return "";
        }

        String status = sapItem.getStatus();
        switch (status) {
            case "Billable":
                return "NotInvoiced";
            case "Invoiced":
                return "Invoiced";
            case "Unbillable":
                return "Unbillable";
            default:
                return status; // Keep original if no mapping
        }
    }

    /**
     * Maps the SAP trip type to canonical trip type
     *
     * @param tripType The SAP trip type
     * @return Canonical trip type
     */
    default String mapTripType(String tripType) {
        if (tripType == null) {
            return "";
        }

        switch (tripType) {
            case "F":
                return "full_trip";
            case "E":
                return "entry_half_trip";
            case "X":
                return "exit_half_trip";
            default:
                return tripType; // Keep original if no mapping
        }
    }

    /**
     * Maps the SAP division to canonical business unit
     *
     * @param division The SAP division
     * @return Canonical business unit
     */
    default String mapBusinessUnit(String division) {
        if (division == null) {
            return "";
        }

        try {
            int divisionInt = Integer.parseInt(division);
            // Define constants for division values
            final int etrDivision = 10;
            final int eastDivision = 20;

            if (divisionInt == etrDivision) {
                return "ETR";
            } else if (divisionInt == eastDivision) {
                return "EAST";
            } else {
                return division; // Keep original if no mapping
            }
        } catch (NumberFormatException e) {
            // If not a number, keep the original value
            return division;
        }
    }

    /**
     * Maps the SAP rating method to canonical rating method
     *
     * @param ratingMethod The SAP rating method
     * @param tripType The canonical trip type (already mapped)
     * @return Canonical rating method
     */
    default String mapRatingMethod(String ratingMethod, String tripType) {
        if (ratingMethod == null) {
            return "";
        }

        if ("full_trip".equals(ratingMethod) && !"full_trip".equals(tripType)) {
            return "minimum_distance_half_trip";
        }

        try {
            int ratingMethodInt = Integer.parseInt(ratingMethod);
            // Define constants for rating method values
            final int flatTollDistanceHalfTrip = 3;
            final int fullTrip = 4;
            final int transponderAverageDistanceHalfTrip = 5;
            final int minimumDistanceHalfTrip = 6;
            final int flatTollHalfTrip = 7;

            switch (ratingMethodInt) {
                case flatTollDistanceHalfTrip:
                    return "flat_toll_distance_half_trip";
                case fullTrip:
                    return "full_trip";
                case transponderAverageDistanceHalfTrip:
                    return "transponder_average_distance_half_trip";
                case minimumDistanceHalfTrip:
                    return "minimum_distance_half_trip";
                case flatTollHalfTrip:
                    return "flat_toll_half_trip";
                default:
                    return ratingMethod; // Keep original if no mapping
            }
        } catch (NumberFormatException e) {
            // If not a number, keep the original value
            return ratingMethod;
        }
    }

    /**
     * Maps the SAP billable item type to canonical billable item type
     *
     * @param billableItemType The SAP billable item type
     * @return Canonical billable item type
     */
    default String mapBillableItemType(String billableItemType) {
        if (billableItemType == null) {
            return "";
        }

        switch (billableItemType) {
            // Plate charges
            case "P100":
                return "Plate_toll_charge";
            case "P102":
                return "Plate_toll_half_trip";
            case "P104":
                return "Plate_toll_flat_trip";
            case "P110":
                return "Camera_charge";
            case "P112":
                return "Camera_charge_half_trip";
            case "P114":
                return "Camera_charge_east";
            case "P116":
                return "Camera_charge_half_trip_east";
            case "P130":
                return "Account_fee";
            case "P140":
                return "Unrecognizable_plate_charge";

            // Transponder charges
            case "T200":
                return "Transponder_toll_charge";
            case "T202":
                return "Transponder_toll_charge_half_trip";
            case "T204":
                return "Transponder_toll_charge_flat_trip";
            case "T206":
                return "Transponder_toll_charge_half_trip_calculated";
            case "T210":
                return "Transponder_trip_toll_charge";
            case "T220":
                return "Lease_monthly_fee";
            case "T222":
                return "Additional_transponder_monthly_lease_fee";
            case "T230":
                return "Lease_annual_fee";
            case "T232":
                return "Additional_transponder_lease_annual_fee";
            case "T234":
                return "Additional_transponder_lease_annual_fee_more_than_6m";
            case "T236":
                return "Additional_transponder_lease_annual_fee_less_than_6m";
            case "T240":
                return "Transponder_replacement_fee";

            // Discount and redemption items
            case "D100":
                return "Discount_item";
            case "R300":
                return "Loyalty_redemption";

            default:
                return billableItemType; // Keep original if no mapping
        }
    }

    /**
     * Maps the SAP rated class to canonical rated class
     *
     * @param ratedClass The SAP rated class
     * @return Canonical rated class
     */
    default String mapRatedClass(String ratedClass) {
        if (ratedClass == null) {
            return "";
        }

        switch (ratedClass) {
            case "2":
                return "light";
            case "3":
                return "heavy";
            default:
                return ratedClass; // Keep original if no mapping
        }
    }

    /**
     * Maps the SAP tolling device indicator to canonical tolling device indicator
     *
     * @param tollingDeviceInd The SAP tolling device indicator
     * @return Canonical tolling device indicator
     */
    default String mapTollingDeviceIndicator(String tollingDeviceInd) {
        if (tollingDeviceInd == null) {
            return "";
        }

        switch (tollingDeviceInd) {
            case "T":
                return "T";
            case "P":
                return "P";
            default:
                return tollingDeviceInd; // Keep original if no mapping
        }
    }

    /**
     * Calculates the trip duration from entry to exit
     *
     * @param sapBillableItem The SAP billable item
     * @return Trip duration in seconds as a string
     */
    default String calcTripDuration(SAPBillableItem sapBillableItem) {
        if (sapBillableItem == null) {
            return "";
        }

        // Define date and time formats
        SimpleDateFormat formatterDate = new SimpleDateFormat("yyyyMMdd");
        SimpleDateFormat formatterTime = new SimpleDateFormat("HHmmss");

        // Initialize variables for start and end times
        long startTime = 0;
        long endTime = 0;
        long startDate = 0;
        long endDate = 0;

        try {
            // Parse and validate entry time
            if (isValid(sapBillableItem.getEntryTime(), "HHmmss")) {
                startTime = formatterTime.parse(sapBillableItem.getEntryTime()).getTime();
            } else {
                return "";
            }

            // Parse and validate exit time
            if (isValid(sapBillableItem.getExitTime(), "HHmmss")) {
                endTime = formatterTime.parse(sapBillableItem.getExitTime()).getTime();
            } else {
                return "";
            }

            // Parse and validate entry date
            if (isValid(sapBillableItem.getEntryDate(), "yyyyMMdd")) {
                startDate = formatterDate.parse(sapBillableItem.getEntryDate()).getTime();
            } else {
                return "";
            }

            // Parse and validate exit date
            if (isValid(sapBillableItem.getExitDate(), "yyyyMMdd")) {
                endDate = formatterDate.parse(sapBillableItem.getExitDate()).getTime();
            } else {
                return "";
            }

            // Calculate the trip duration
            long start = startTime + startDate;
            long end = endTime + endDate;
            long tripDuration = end - start;
            // Convert from milliseconds to seconds
            final int millisecondsToSeconds = 1000;
            return Long.toString(tripDuration / millisecondsToSeconds);
        } catch (ParseException e) {
            return "";
        }
    }

    /**
     * Validates if a date or time string matches the expected format
     *
     * @param value The date or time string to validate
     * @param format The expected format
     * @return True if valid, false otherwise
     */
    default boolean isValid(String value, String format) {
        if (value == null || value.isEmpty()) {
            return false;
        }

        SimpleDateFormat sdf = new SimpleDateFormat(format);
        sdf.setLenient(false);

        try {
            sdf.parse(value);
            return true;
        } catch (ParseException e) {
            return false;
        }
    }

    /**
     * Default string mapping method to avoid ambiguity in MapStruct
     *
     * @param value The input string
     * @return The same string or empty string if null
     */
    @Named("defaultStringMapping")
    default String defaultStringMapping(String value) {
        return value != null ? value : "";
    }
}
