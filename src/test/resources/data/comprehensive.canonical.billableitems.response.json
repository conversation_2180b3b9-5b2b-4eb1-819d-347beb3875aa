{"items": [{"billableItemAmt": "10.00", "billableItemText": "Plate toll charge", "billableItemType": "Plate_toll_charge", "billingDocumentId": "000000000001", "billingQuantity": "1.**************", "billingCustomerId": "**********", "dateOfOrigin": "********", "businessUnit": "ETR", "statusOfBillableItem": "NotInvoiced", "tripId": "MOCK001", "tripType": "full_trip"}, {"billableItemAmt": "5.00", "billableItemText": "Plate toll half trip", "billableItemType": "Plate_toll_half_trip", "billingDocumentId": "000000000002", "billingQuantity": "1.**************", "billingCustomerId": "**********", "dateOfOrigin": "********", "businessUnit": "ETR", "statusOfBillableItem": "NotInvoiced", "tripId": "MOCK002", "tripType": "entry_half_trip"}, {"billableItemAmt": "15.00", "billableItemText": "Plate toll flat trip", "billableItemType": "Plate_toll_flat_trip", "billingDocumentId": "000000000003", "billingQuantity": "1.**************", "billingCustomerId": "**********", "dateOfOrigin": "********", "businessUnit": "ETR", "statusOfBillableItem": "NotInvoiced", "tripId": "MOCK003", "tripType": "full_trip"}, {"billableItemAmt": "5.20", "billableItemText": "Camera charge", "billableItemType": "Camera_charge", "billingDocumentId": "000000000004", "billingQuantity": "1.**************", "billingCustomerId": "**********", "dateOfOrigin": "********", "businessUnit": "ETR", "statusOfBillableItem": "NotInvoiced", "tripId": "MOCK004", "tripType": "full_trip"}, {"billableItemAmt": "2.60", "billableItemText": "Camera charge half trip", "billableItemType": "Camera_charge_half_trip", "billingDocumentId": "000000000005", "billingQuantity": "1.**************", "billingCustomerId": "**********", "dateOfOrigin": "********", "businessUnit": "ETR", "statusOfBillableItem": "NotInvoiced", "tripId": "MOCK005", "tripType": "entry_half_trip"}, {"billableItemAmt": "5.20", "billableItemText": "Camera charge east", "billableItemType": "Camera_charge_east", "billingDocumentId": "000000000006", "billingQuantity": "1.**************", "billingCustomerId": "**********", "dateOfOrigin": "********", "businessUnit": "EAST", "statusOfBillableItem": "NotInvoiced", "tripId": "MOCK006", "tripType": "full_trip"}, {"billableItemAmt": "2.60", "billableItemText": "Camera charge half trip east", "billableItemType": "Camera_charge_half_trip_east", "billingDocumentId": "************", "billingQuantity": "1.**************", "billingCustomerId": "**********", "dateOfOrigin": "********", "businessUnit": "EAST", "statusOfBillableItem": "NotInvoiced", "tripId": "MOCK007", "tripType": "entry_half_trip"}, {"billableItemAmt": "3.95", "billableItemText": "Account fee", "billableItemType": "Account_fee", "billingDocumentId": "************", "billingQuantity": "1.**************", "billingCustomerId": "**********", "dateOfOrigin": "********", "businessUnit": "ETR", "statusOfBillableItem": "NotInvoiced", "tripId": "MOCK008", "tripType": "full_trip"}, {"billableItemAmt": "22.50", "billableItemText": "Unrecognizable plate charge", "billableItemType": "Unrecognizable_plate_charge", "billingDocumentId": "************", "billingQuantity": "1.**************", "billingCustomerId": "**********", "dateOfOrigin": "********", "businessUnit": "ETR", "statusOfBillableItem": "NotInvoiced", "tripId": "MOCK009", "tripType": "full_trip"}, {"billableItemAmt": "8.50", "billableItemText": "Transponder toll charge", "billableItemType": "Transponder_toll_charge", "billingDocumentId": "************", "billingQuantity": "1.**************", "billingCustomerId": "**********", "dateOfOrigin": "********", "businessUnit": "ETR", "statusOfBillableItem": "NotInvoiced", "tripId": "MOCK010", "tripType": "full_trip"}, {"billableItemAmt": "4.25", "billableItemText": "Transponder toll charge half trip", "billableItemType": "Transponder_toll_charge_half_trip", "billingDocumentId": "000000000011", "billingQuantity": "1.**************", "billingCustomerId": "**********", "dateOfOrigin": "********", "businessUnit": "ETR", "statusOfBillableItem": "NotInvoiced", "tripId": "MOCK011", "tripType": "entry_half_trip"}, {"billableItemAmt": "12.75", "billableItemText": "Transponder toll charge flat trip", "billableItemType": "Transponder_toll_charge_flat_trip", "billingDocumentId": "000000000012", "billingQuantity": "1.**************", "billingCustomerId": "**********", "dateOfOrigin": "********", "businessUnit": "ETR", "statusOfBillableItem": "NotInvoiced", "tripId": "MOCK012", "tripType": "full_trip"}, {"billableItemAmt": "4.25", "billableItemText": "Transponder toll charge half trip calculated", "billableItemType": "Transponder_toll_charge_half_trip_calculated", "billingDocumentId": "000000000013", "billingQuantity": "1.**************", "billingCustomerId": "**********", "dateOfOrigin": "********", "businessUnit": "ETR", "statusOfBillableItem": "NotInvoiced", "tripId": "MOCK013", "tripType": "entry_half_trip"}, {"billableItemAmt": "8.50", "billableItemText": "Transponder trip toll charge", "billableItemType": "Transponder_trip_toll_charge", "billingDocumentId": "000000000014", "billingQuantity": "1.**************", "billingCustomerId": "**********", "dateOfOrigin": "********", "businessUnit": "ETR", "statusOfBillableItem": "NotInvoiced", "tripId": "MOCK014", "tripType": "full_trip"}, {"billableItemAmt": "3.95", "billableItemText": "Lease monthly fee", "billableItemType": "Lease_monthly_fee", "billingDocumentId": "000000000015", "billingQuantity": "1.**************", "billingCustomerId": "**********", "dateOfOrigin": "********", "businessUnit": "ETR", "statusOfBillableItem": "NotInvoiced", "tripId": "MOCK015", "tripType": "full_trip"}, {"billableItemAmt": "1.95", "billableItemText": "Additional transponder monthly lease fee", "billableItemType": "Additional_transponder_monthly_lease_fee", "billingDocumentId": "000000000016", "billingQuantity": "1.**************", "billingCustomerId": "**********", "dateOfOrigin": "********", "businessUnit": "ETR", "statusOfBillableItem": "NotInvoiced", "tripId": "MOCK016", "tripType": "full_trip"}, {"billableItemAmt": "47.40", "billableItemText": "Lease annual fee", "billableItemType": "Lease_annual_fee", "billingDocumentId": "000000000017", "billingQuantity": "1.**************", "billingCustomerId": "**********", "dateOfOrigin": "********", "businessUnit": "ETR", "statusOfBillableItem": "NotInvoiced", "tripId": "MOCK017", "tripType": "full_trip"}, {"billableItemAmt": "23.40", "billableItemText": "Additional transponder lease annual fee", "billableItemType": "Additional_transponder_lease_annual_fee", "billingDocumentId": "000000000018", "billingQuantity": "1.**************", "billingCustomerId": "**********", "dateOfOrigin": "********", "businessUnit": "ETR", "statusOfBillableItem": "NotInvoiced", "tripId": "MOCK018", "tripType": "full_trip"}, {"billableItemAmt": "35.40", "billableItemText": "Additional transponder lease annual fee more than 6m", "billableItemType": "Additional_transponder_lease_annual_fee_more_than_6m", "billingDocumentId": "000000000019", "billingQuantity": "1.**************", "billingCustomerId": "**********", "dateOfOrigin": "********", "businessUnit": "ETR", "statusOfBillableItem": "NotInvoiced", "tripId": "MOCK019", "tripType": "full_trip"}, {"billableItemAmt": "11.40", "billableItemText": "Additional transponder lease annual fee less than 6m", "billableItemType": "Additional_transponder_lease_annual_fee_less_than_6m", "billingDocumentId": "000000000020", "billingQuantity": "1.**************", "billingCustomerId": "**********", "dateOfOrigin": "********", "businessUnit": "ETR", "statusOfBillableItem": "NotInvoiced", "tripId": "MOCK020", "tripType": "full_trip"}, {"billableItemAmt": "25.00", "billableItemText": "Transponder replacement fee", "billableItemType": "Transponder_replacement_fee", "billingDocumentId": "000000000021", "billingQuantity": "1.**************", "billingCustomerId": "**********", "dateOfOrigin": "********", "businessUnit": "ETR", "statusOfBillableItem": "NotInvoiced", "tripId": "MOCK021", "tripType": "full_trip"}, {"billableItemAmt": "-5.00", "billableItemText": "Discount item", "billableItemType": "Discount_item", "billingDocumentId": "000000000022", "billingQuantity": "1.**************", "billingCustomerId": "**********", "dateOfOrigin": "********", "businessUnit": "ETR", "statusOfBillableItem": "NotInvoiced", "tripId": "MOCK022", "tripType": "full_trip"}, {"billableItemAmt": "-10.00", "billableItemText": "Loyalty redemption", "billableItemType": "Loyalty_redemption", "billingDocumentId": "000000000023", "billingQuantity": "1.**************", "billingCustomerId": "**********", "dateOfOrigin": "********", "businessUnit": "ETR", "statusOfBillableItem": "NotInvoiced", "tripId": "MOCK023", "tripType": "full_trip"}]}